import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../widgets/app_drawer.dart';
import '../widgets/banner_carousel.dart';
import '../screens/explore_products_page.dart';
import '../models/models.dart';
import '../theme/app_colors.dart';
import '../bloc/bloc.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Enhanced app bar with proper theming and professional design
      appBar: _buildAppBar(context),
      // Modern, production-grade drawer
      drawer: const AppDrawer(),
      body: BlocBuilder<MedicineCubit, MedicineState>(
        builder: (context, medicineState) {
          return RefreshIndicator(
            onRefresh: () => context.read<MedicineCubit>().refreshMedicines(),
            color: AppColors.primary,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Banner carousel section
                  const BannerCarousel(),

                  const SizedBox(height: 16),

                  if (medicineState is MedicineLoaded) ...[
                    // Compact search and quick filters section
                    _buildCompactSearchSection(context, medicineState),

                    const SizedBox(height: 16),

                    // Quick access section
                    _buildQuickAccessSection(context),

                    const SizedBox(height: 16),

                    // Featured medicines section
                    _buildFeaturedMedicinesSection(context, medicineState),
                  ] else if (medicineState is MedicineLoading) ...[
                    const Center(
                      child: Padding(
                        padding: EdgeInsets.all(32.0),
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  ] else if (medicineState is MedicineError) ...[
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(32.0),
                        child: Column(
                          children: [
                            const Icon(Icons.error_outline,
                                size: 64, color: AppColors.error),
                            const SizedBox(height: 16),
                            Text(
                              'Error loading medicines',
                              style: Theme.of(context).textTheme.headlineSmall,
                            ),
                            const SizedBox(height: 8),
                            Text(medicineState.message),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: () =>
                                  context.read<MedicineCubit>().loadMedicines(),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],

                  const SizedBox(height: 24), // Bottom padding
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Builds the app bar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: const Text(
        'Online Medicine',
        style: TextStyle(
          color: AppColors.textOnPrimary,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      leading: Builder(
        builder: (context) => IconButton(
          icon: const Icon(
            Icons.menu,
            color: AppColors.textOnPrimary,
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      actions: [
        // Notification icon
        IconButton(
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Notifications feature coming soon!'),
                backgroundColor: AppColors.primary,
              ),
            );
          },
          icon: const Icon(
            Icons.notifications_outlined,
            color: AppColors.textOnPrimary,
          ),
        ),

        // Profile avatar
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: GestureDetector(
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Profile feature coming soon!'),
                  backgroundColor: AppColors.primary,
                ),
              );
            },
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.person_outline,
                color: AppColors.textOnPrimary,
                size: 20,
              ),
            ),
          ),
        ),

        const SizedBox(width: 12), // Right padding
      ],
    );
  }

  /// Builds compact search section with quick filters
  Widget _buildCompactSearchSection(
      BuildContext context, MedicineLoaded state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Search bar
          Container(
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.borderLight),
              boxShadow: const [
                BoxShadow(
                  color: AppColors.shadowLight,
                  offset: Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child: TextField(
              onChanged: (query) =>
                  context.read<MedicineCubit>().updateSearchQuery(query),
              decoration: InputDecoration(
                hintText: 'Search medicines, brands...',
                prefixIcon:
                    const Icon(Icons.search, color: AppColors.textSecondary),
                suffixIcon: IconButton(
                  onPressed: () => _navigateToExploreProducts(context),
                  icon: const Icon(Icons.tune, color: AppColors.primary),
                  tooltip: 'Advanced Filters',
                ),
                border: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Quick brand filters (only show top 5 brands)
          if (state.brands.isNotEmpty) ...[
            SizedBox(
              height: 35,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: state.brands.length > 5 ? 5 : state.brands.length,
                itemBuilder: (context, index) {
                  final brand = state.brands[index];
                  final isSelected = state.selectedBrand == brand;

                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(brand),
                      selected: isSelected,
                      onSelected: (selected) {
                        context
                            .read<MedicineCubit>()
                            .updateBrandFilter(selected ? brand : null);
                      },
                      backgroundColor: AppColors.surfaceVariant,
                      selectedColor: AppColors.primary.withOpacity(0.1),
                      labelStyle: TextStyle(
                        color: isSelected
                            ? AppColors.primary
                            : AppColors.textSecondary,
                        fontSize: 12,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.primary
                            : AppColors.borderMedium,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Builds quick access section with special offers and trending
  Widget _buildQuickAccessSection(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          // Special Offers
          Expanded(
            child: _buildQuickAccessCard(
              title: 'Special Offers',
              subtitle: 'Up to 50% OFF',
              icon: Icons.local_offer,
              color: AppColors.secondary,
              onTap: () =>
                  _navigateToExploreProducts(context, category: 'specialOffer'),
            ),
          ),

          const SizedBox(width: 12),

          // Trending Products
          Expanded(
            child: _buildQuickAccessCard(
              title: 'Trending',
              subtitle: 'Popular medicines',
              icon: Icons.trending_up,
              color: AppColors.accent,
              onTap: () =>
                  _navigateToExploreProducts(context, category: 'trending'),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds individual quick access card
  Widget _buildQuickAccessCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                color: color.withOpacity(0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds featured medicines section with limited items and "View All" button
  Widget _buildFeaturedMedicinesSection(
      BuildContext context, MedicineLoaded state) {
    // Show only first 3-4 medicines as featured
    final featuredMedicines = state.medicines.take(4).toList();

    return Column(
      children: [
        // Section header
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Featured Medicines',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              TextButton(
                onPressed: () => _navigateToExploreProducts(context),
                child: const Text(
                  'View All',
                  style: TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Featured medicines list
        BlocBuilder<FavoritesCubit, FavoritesState>(
          builder: (context, favoritesState) {
            return BlocBuilder<CartCubit, CartState>(
              builder: (context, cartState) {
                final favorites = <String, bool>{};
                final cartItems = <String, int>{};

                if (favoritesState is FavoritesLoaded) {
                  for (final item in favoritesState.items) {
                    favorites[item.id] = true;
                  }
                }

                if (cartState is CartLoaded) {
                  for (final item in cartState.items) {
                    cartItems[item.id] = item.cartQuantity;
                  }
                }

                return ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: featuredMedicines.length,
                  itemBuilder: (context, index) {
                    final medicine = featuredMedicines[index];
                    return Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      child: _buildCompactMedicineCard(
                        medicine: medicine,
                        isFavorite: favorites[medicine.id] ?? false,
                        cartQuantity: cartItems[medicine.id] ?? 0,
                        onFavoriteToggle: () => context
                            .read<FavoritesCubit>()
                            .toggleFavorite(medicine),
                        onAddToCart: () =>
                            context.read<CartCubit>().addToCart(medicine, 1),
                      ),
                    );
                  },
                );
              },
            );
          },
        ),
      ],
    );
  }

  /// Navigate to explore products page with optional category
  void _navigateToExploreProducts(BuildContext context, {String? category}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => ExploreProductsCubit()..loadProducts(),
          child: const ExploreProductsPage(),
        ),
      ),
    );
  }

  /// Builds compact medicine card for homepage
  Widget _buildCompactMedicineCard({
    required Medicine medicine,
    required bool isFavorite,
    required int cartQuantity,
    required VoidCallback onFavoriteToggle,
    required VoidCallback onAddToCart,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          // Medicine image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.surfaceVariant,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.borderLight),
            ),
            child: medicine.imageUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      medicine.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Icon(
                          Icons.medical_services,
                          color: AppColors.textSecondary,
                          size: 24,
                        );
                      },
                    ),
                  )
                : const Icon(
                    Icons.medical_services,
                    color: AppColors.textSecondary,
                    size: 24,
                  ),
          ),

          const SizedBox(width: 12),

          // Medicine details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  medicine.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  '${medicine.quantity} • ${medicine.brand}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '৳${medicine.effectivePrice.toStringAsFixed(0)}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.primary,
                      ),
                    ),
                    if (medicine.hasDiscount) ...[
                      const SizedBox(width: 6),
                      Text(
                        '৳${medicine.regularPrice.toStringAsFixed(0)}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textTertiary,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Action buttons
          Column(
            children: [
              // Favorite button
              IconButton(
                onPressed: onFavoriteToggle,
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? AppColors.error : AppColors.textSecondary,
                  size: 20,
                ),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: EdgeInsets.zero,
              ),

              // Add to cart button
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: IconButton(
                  onPressed: onAddToCart,
                  icon: const Icon(
                    Icons.add,
                    color: AppColors.textOnPrimary,
                    size: 16,
                  ),
                  constraints:
                      const BoxConstraints(minWidth: 32, minHeight: 32),
                  padding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
